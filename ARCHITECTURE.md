# 🏗️ Arquitetura do Projeto - PIC-894 NGFW SSL Decrypt

Este documento apresenta a arquitetura e diagramas do projeto de validação SSL Decrypt no NGFW (Next Generation Firewall).

## 📋 Índice

- [Visão Geral](#-visão-geral)
- [C4 Model - Contexto](#-c4-model---contexto)
- [C4 Model - Containers](#-c4-model---containers)
- [Fluxo de Arquitetura](#-fluxo-de-arquitetura)
- [Diagrama de Sequência](#-diagrama-de-sequência)
- [Estrutura de Componentes](#-estrutura-de-componentes)
- [Tecnologias](#-tecnologias)

## 🎯 Visão Geral

O projeto implementa aplicações em **três linguagens** (Python, Java, Node.js) para validar a funcionalidade de SSL Decrypt no NGFW, garantindo a inspeção segura do tráfego Norte-Sul entre workloads em cloud.

## 🌐 C4 Model - Contexto

```mermaid
C4Context
    title Contexto do Sistema - PIC-894 NGFW SSL Decrypt

    Person(dev, "Desenvolvedor", "Equipe CPE")
    Person(security, "Analista de Segurança", "Segurança da Informação")

    System_Boundary(porto, "Porto Seguro") {
        System(ngfw_system, "Sistema NGFW SSL Decrypt", "Validação de interceptação e descriptografia de tráfego HTTPS")
    }

    System_Ext(dogapi, "Dog API", "API externa para teste de conectividade HTTPS")
    System_Ext(techstore, "Tech Store (IDP)", "Plataforma de deploy e CI/CD")
    System_Ext(eks, "EKS Cluster", "Kubernetes cluster na AWS")

    Rel(dev, ngfw_system, "Desenvolve e testa", "HTTPS")
    Rel(security, ngfw_system, "Valida segurança", "Monitoramento")
    Rel(ngfw_system, dogapi, "Requisições HTTPS", "SSL/TLS")
    Rel(techstore, ngfw_system, "Deploy automático", "CI/CD")
    Rel(ngfw_system, eks, "Executa em", "Kubernetes")

    UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="1")
```

## 📦 C4 Model - Containers

```mermaid
C4Container
    title Container Level - Aplicações NGFW SSL Decrypt

    Person(user, "Usuário/Tester", "Valida funcionalidade")

    System_Boundary(ngfw_boundary, "Sistema NGFW SSL Decrypt") {
        Container(python_app, "Python App", "Flask", "Aplicação Python com requests para validação SSL")
        Container(java_app, "Java App", "Spring Boot", "Aplicação Java com Feign Client")
        Container(nodejs_app, "Node.js App", "Express + TypeScript", "Aplicação Node.js com cliente HTTP")

        Container(ngfw, "NGFW", "Next Generation Firewall", "Intercepta e descriptografa tráfego SSL/TLS")
    }

    System_Boundary(infra, "Infraestrutura AWS") {
        Container(eks_pod1, "Pod Python", "Kubernetes", "Container Python no EKS")
        Container(eks_pod2, "Pod Java", "Kubernetes", "Container Java no EKS")
        Container(eks_pod3, "Pod Node.js", "Kubernetes", "Container Node.js no EKS")
    }

    System_Ext(dogapi, "Dog Breeds API", "dogapi.dog", "API externa para teste")
    System_Ext(techstore, "Tech Store", "IDP Porto", "Plataforma de deploy")

    Rel(user, python_app, "Testa endpoint", "HTTPS")
    Rel(user, java_app, "Testa endpoint", "HTTPS")
    Rel(user, nodejs_app, "Testa endpoint", "HTTPS")

    Rel(python_app, ngfw, "Tráfego HTTPS", "SSL/TLS")
    Rel(java_app, ngfw, "Tráfego HTTPS", "SSL/TLS")
    Rel(nodejs_app, ngfw, "Tráfego HTTPS", "SSL/TLS")

    Rel(ngfw, dogapi, "Requisição interceptada", "HTTPS descriptografado")

    Rel(python_app, eks_pod1, "Executa em")
    Rel(java_app, eks_pod2, "Executa em")
    Rel(nodejs_app, eks_pod3, "Executa em")

    Rel(techstore, eks_pod1, "Deploy")
    Rel(techstore, eks_pod2, "Deploy")
    Rel(techstore, eks_pod3, "Deploy")

    UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="2")
```

## 🔄 Fluxo de Arquitetura

```mermaid
flowchart TD
    A[👨‍💻 Desenvolvedor] --> B[🚀 Tech Store IDP]
    B --> C[📦 Deploy no EKS]

    subgraph EKS["🏗️ EKS Cluster (us-east-1)"]
        D[🐍 Pod Python<br/>Flask + requests]
        E[☕ Pod Java<br/>Spring Boot + Feign]
        F[🟢 Pod Node.js<br/>Express + TypeScript]
    end

    C --> D
    C --> E
    C --> F

    subgraph NGFW["🔒 NGFW SSL Decrypt"]
        G[🔍 Interceptação]
        H[🔓 Descriptografia]
        I[📊 Inspeção]
    end

    D --> G
    E --> G
    F --> G

    G --> H
    H --> I
    I --> J[🌐 Dog API<br/>dogapi.dog/api/v2/breeds]

    J --> K[📋 Resposta JSON<br/>Dados das raças]
    K --> I
    I --> H
    H --> G

    G --> L[✅ Validação<br/>SSL Decrypt]
    L --> M[📈 Monitoramento<br/>Segurança]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style EKS fill:#e8f5e8
    style NGFW fill:#fff3e0
    style J fill:#fce4ec
    style L fill:#e0f2f1
    style M fill:#f1f8e9
```

## 📊 Diagrama de Sequência

```mermaid
sequenceDiagram
    participant Dev as 👨‍💻 Desenvolvedor
    participant App as 📱 Aplicação
    participant NGFW as 🔒 NGFW
    participant API as 🌐 Dog API
    participant Monitor as 📊 Monitoramento

    Note over Dev, Monitor: Fluxo de Validação SSL Decrypt

    Dev->>App: 1. Acessa endpoint /dog-breeds
    activate App

    App->>NGFW: 2. Requisição HTTPS
    Note right of App: GET https://dogapi.dog/api/v2/breeds
    activate NGFW

    NGFW->>NGFW: 3. Intercepta tráfego SSL
    NGFW->>NGFW: 4. Descriptografa SSL/TLS
    NGFW->>Monitor: 5. Log de inspeção

    NGFW->>API: 6. Encaminha requisição
    activate API

    API-->>NGFW: 7. Resposta JSON (raças)
    deactivate API

    NGFW->>NGFW: 8. Inspeciona resposta
    NGFW->>Monitor: 9. Log de validação
    NGFW-->>App: 10. Retorna resposta
    deactivate NGFW

    App-->>Dev: 11. Dados das raças
    deactivate App

    Note over Dev, Monitor: ✅ Validação SSL Decrypt Concluída

    Monitor->>Dev: 12. Relatório de segurança
```

## 🧩 Estrutura de Componentes

```mermaid
graph TB
    subgraph Python["🐍 Aplicação Python"]
        PY1[dog_service.py<br/>Service Layer]
        PY2[dog_routes.py<br/>Route Handler]
        PY3[config.py<br/>SSL Config]
        PY1 --> PY2
        PY3 --> PY1
    end

    subgraph Java["☕ Aplicação Java"]
        JV1[DogApiController<br/>REST Controller]
        JV2[DogApiClient<br/>Feign Client]
        JV3[application.yml<br/>Configuration]
        JV2 --> JV1
        JV3 --> JV2
    end

    subgraph NodeJS["🟢 Aplicação Node.js"]
        JS1[ClienteController.ts<br/>Controller]
        JS2[app.ts<br/>Express App]
        JS3[routes.ts<br/>Route Config]
        JS1 --> JS2
        JS3 --> JS2
    end

    subgraph External["🌐 Serviços Externos"]
        EXT1[Dog API<br/>dogapi.dog]
        EXT2[NGFW<br/>SSL Decrypt]
        EXT3[Kubernetes<br/>EKS Pods]
    end

    Python --> EXT2
    Java --> EXT2
    NodeJS --> EXT2
    EXT2 --> EXT1

    Python -.-> EXT3
    Java -.-> EXT3
    NodeJS -.-> EXT3

    style Python fill:#306998,color:#fff
    style Java fill:#f89820,color:#fff
    style NodeJS fill:#68a063,color:#fff
    style External fill:#e1f5fe
```

## 🛠️ Tecnologias

### **Backend**

- **Python:** Flask, requests, logging
- **Java:** Spring Boot, Feign Client, Maven
- **Node.js:** Express, TypeScript, Morgan

### **Infraestrutura**

- **Container:** Docker
- **Orquestração:** Kubernetes (EKS)
- **Cloud:** AWS (us-east-1)
- **Deploy:** Tech Store (IDP)

### **Segurança**

- **Firewall:** NGFW com SSL Decrypt
- **Certificados:** SSL/TLS automático
- **Monitoramento:** Prometheus, Actuator

---

**📋 Resumo:** Esta arquitetura demonstra a implementação multi-linguagem para validação de SSL Decrypt no NGFW, garantindo inspeção segura do tráfego HTTPS em ambiente cloud.
