# PIC-894 - Implementações em Python, Java e Node.js para SSL Decrypt no NGFW

**Projeto:** PIC-894 - Next Generation Firewall SSL Decrypt
**Autor:** <PERSON>
**Data:** 2024

---

## 📋 Descrição Geral

Esta aplicação foi desenvolvida com o intuito de **validar a funcionalidade de SSL Decrypt no NGFW** (Next Generation Firewall), garantindo a inspeção segura do tráfego Norte-Sul entre workloads em cloud, conforme definido pela equipe de CPE em conjunto com as áreas de Segurança da Informação e Riscos Cibernéticos.

### 🎯 Objetivo

Entregar aplicações em **três linguagens diferentes** (Java, Node.js e Python) no ambiente `cpe-dev` (`eks-cpe-dev`) para testes da funcionalidade de SSL Decrypt.

## 🚀 Funcionalidade Principal

A aplicação realiza uma requisição externa para a seguinte URL pública, que retorna informações sobre raças de cachorro:

```
https://dogapi.dog/api/v2/breeds
```

### 📊 Fluxo de Funcionamento

1. **Requisição HTTPS** → API externa (dogapi.dog)
2. **Interceptação** → NGFW com SSL Decrypt ativo
3. **Descriptografia** → Inspeção do tráfego pelo firewall
4. **Resposta** → Dados retornados para a aplicação

O retorno é exibido diretamente como resposta HTTP da aplicação, permitindo verificar se o tráfego está sendo corretamente interceptado e descriptografado pelo NGFW.

## 🔐 Observações sobre Certificados SSL

### ✅ Java e Node.js

- **Configuração:** Nenhuma configuração adicional necessária
- **Certificados:** Já incluídos na imagem base do IDP da Porto
- **Validação:** Truststore e keystore configurados automaticamente

### ⚙️ Python

- **Configuração:** Variáveis de ambiente necessárias
- **Motivo:** Interpretador Python precisa reconhecer certificados da imagem
- **Resultado:** Bibliotecas como `requests` validam conexões HTTPS normalmente

## 💻 Estrutura do Código das Aplicações

### 🐍 Python

#### 1. Service Layer - `dog_service.py`

```python
import requests
from api.core.logs import log_formatted
from api.config import REQUESTS_CA_BUNDLE

DOG_API_URL = "https://dogapi.dog/api/v2/breeds"

def get_dog_breeds():
    """
    Busca informações sobre raças de cachorro da API externa.

    Returns:
        dict: Dados das raças de cachorro em formato JSON

    Raises:
        requests.exceptions.RequestException: Erro na requisição HTTP
    """
    response = requests.get(DOG_API_URL, verify=REQUESTS_CA_BUNDLE)
    response.raise_for_status()
    return response.json()
```

#### 2. Route Handler - `dog-breeds`

```python
from flask import Blueprint, request, jsonify, Response
from api.core.logs import log_formatted
from api.core.security import requires_roles, token_required
from api.services.dog_service import get_dog_breeds

clients = Blueprint("clients", __name__)

@clients.route("/dog-breeds", methods=["GET"])
def dog_breeds():
    """
    Endpoint para buscar raças de cachorro.

    Returns:
        Response: JSON com dados das raças ou erro
    """
    try:
        data = get_dog_breeds()
        log_formatted.info("Dados de raças obtidos com sucesso")
        return jsonify(data), 200
    except Exception as e:
        log_formatted.error(f"Erro ao acessar Dog API: {e}")
        return jsonify({"error": str(e)}), 500
```

#### 3. Configuração de Ambiente - `values_dev.yml`

```yaml
# Configurações de certificados SSL para Python
environment:
  REQUESTS_CA_BUNDLE: "/etc/ssl/cert.pem"
  SSL_CERT_FILE: "/etc/ssl/cert.pem"
  CURL_CA_BUNDLE: "/etc/ssl/cert.pem"
```

> **💡 Nota:** Essas variáveis garantem que o Python reconheça os certificados SSL da imagem base.

### ☕ Java

#### 1. Controller - `DogApiController`

```java
@RestController
@Service
public class DogApiController implements ApiApi {

    @Autowired
    private DogApiClient dogApiClient;

    /**
     * Endpoint para listar raças de cachorro.
     *
     * @return ResponseEntity com dados das raças ou erro
     */
    @Override
    public ResponseEntity<ListarRacas> listarRacas() {
        try {
            var clientReturn = dogApiClient.getBreeds();
            System.out.println(clientReturn);
            return ResponseEntity.ok(clientReturn);
        } catch (FeignException feignEX) {
            return ResponseEntity
                .status(feignEX.status())
                .body(Map.of(
                    "erro", "Erro ao consumir a API",
                    "status", feignEX.status(),
                    "mensagem", feignEX.getMessage()
                ));
        } catch (Exception ex) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "erro", "Erro interno ao processar a requisição",
                    "mensagem", ex.getMessage()
                ));
        }
    }
}
```

#### 2. Feign Client - `DogApiClient`

```java
@Component
@FeignClient(value = "sample", url = "${dog.api.url}")
public interface DogApiClient {

    /**
     * Busca dados de raças de cachorro da API externa.
     *
     * @return DTO com informações das raças
     */
    @RequestMapping(method = RequestMethod.GET)
    DogBreedsResponseGeneratedDto getBreeds();
}
```

> **💡 Nota:** O Feign Client simplifica as chamadas HTTP e integra automaticamente com o Spring Boot.

### 🟢 Node.js

#### 1. Controller - `ClienteController.ts`

````typescript
import logger from "@/config/logger";
import clienteRepository from "@/repository/clienteRepository";
import { iCliente } from "@/types/iCliente";
import { Request, Response } from "express";

/**
 * Controller responsável pelas operações de cliente e teste de conectividade.
 */
class ClienteController {

    /**
     * Obtém lista de clientes (usado para teste de conectividade).
     */
    obterClientes = async (_req: Request, _res: Response) => {
        logger.debug("Obtendo clientes");
        return _res.json(await clienteRepository.obterClientes());
    };

    /**
     * Remove um cliente específico.
     */
    deletarCliente = async (_req: Request, _res: Response) => {
        const { codigo } = _req.params;
        logger.debug(`Excluindo o cliente ${codigo}`);
        const retorno = await clienteRepository.deletarCliente(Number(codigo));
        return _res.status(201).json(retorno);
    };

    /**
     * Insere um novo cliente.
     */
    inserirCliente = async (_req: Request, _res: Response) => {
        const cliente: iCliente = _req.body;
        logger.debug("Inserindo o cliente %o", cliente);
        const retorno = await clienteRepository.inserirCliente(cliente);
        return _res.status(201).json(retorno);
    };

    /**
     * Atualiza dados de um cliente existente.
     */
    atualizarCliente = async (_req: Request, _res: Response) => {
        const { codigo } = _req.params;
        const cliente: iCliente = _req.body;
        const retorno = await clienteRepository.atualizarCliente(
            Number(codigo),
            cliente
        );
        return _res.status(201).json(retorno);
    };
}

export default new ClienteController();
```

#### 2. Aplicação Principal - `app.ts`

```typescript
import "express-async-errors";
import cors from "cors";
import express, { Request, Response, NextFunction } from "express";
import routes from "@/routers/routes";
import actuator from "express-actuator";
import swaggerUI from "swagger-ui-express";
import cookieParser from "cookie-parser";
import { ActuatorConfig } from "@/config/actuator";
import CONFIG from "@/config";
import { configurarSwagger } from "@/config/swagger";
import helmet from "helmet";
import { validarToken } from "@/config/SecurityConfig";
import morgan from "morgan";
import SqsConsumer from "@/samples/SqsConsumer";
import client from "prom-client";
import httpRequestDurationMicroseconds from "@/config/prometheus";

/**
 * Cria e configura a aplicação Express.
 *
 * @returns Aplicação Express configurada
 */
export const createApp = (): express.Application => {
    const contexto = CONFIG.APP.CONTEXTO as string;
    const app = express();
    const register = new client.Registry();

    // Configuração de métricas Prometheus
    client.collectDefaultMetrics({ register });
    app.get("/metrics", async (_req, _res) => {
        _res.setHeader("Content-Type", register.contentType);
        _res.send(await register.metrics());
    });

    // Middlewares básicos
    app.use(cors());
    app.use(express.json());
    app.use(cookieParser());
    app.use(express.urlencoded({ extended: true }));

    // Configuração do Actuator
    app.use(`/${contexto}`, actuator(ActuatorConfig));

    // Configuração do Swagger
    const swaggerDocument = configurarSwagger(`${contexto}/api/v1`);
    const options = {
        swaggerOptions: {
            url: `/${contexto}/v1/api-docs`,
        },
    };
    app.get(`/${contexto}/v1/api-docs`, (_req, res) => res.json(swaggerDocument));
    app.use(
        `/${contexto}/swagger-ui.html`,
        swaggerUI.serveFiles(swaggerDocument, options),
        swaggerUI.setup(null, options)
    );

    // Configuração de segurança
    app.use(
        helmet({
            crossOriginEmbedderPolicy: false,
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    upgradeInsecureRequests: [],
                },
            },
        })
    );

    // Middlewares de aplicação
    app.use(validarToken);
    app.use(morgan("combined"));
    app.use(`/${contexto}/api/v1`, routes);

    // Middleware de métricas de performance
    app.use((_req: Request, res: Response, next: NextFunction) => {
        res.locals.startEpoch = Date.now();
        const responseTimeInMs: number = Date.now() - res.locals.startEpoch;
        httpRequestDurationMicroseconds
            .labels(_req.method, _req.url, res.statusCode.toString())
            .observe(responseTimeInMs);
        next();
    });

    // Inicialização do consumer SQS
    if (SqsConsumer) {
        SqsConsumer.startListening();
    }

    return app;
};
````

> **💡 Nota:** A aplicação Node.js inclui configurações completas de segurança, monitoramento e documentação.

## 🚀 Deploy e Infraestrutura

### 📦 Ambiente de Deploy

- **Plataforma:** Tech Store (IDP)
- **Ambiente:** `cpe-dev`
- **Infraestrutura:** Pod no EKS (`us-east-1: eks-cpe-dev`)
- **Conectividade:** Acesso à Internet para validação da URL externa

### 🔄 Processo de Deploy

1. **Build** das aplicações via pipeline CI/CD
2. **Deploy** automático no ambiente `cpe-dev`
3. **Validação** de conectividade com API externa
4. **Teste** de funcionalidade SSL Decrypt

## ✅ Critérios de Aceite (DoD)

- [x] **Conectividade HTTPS:** Aplicação responde com dados das raças de cachorro via HTTPS
- [x] **Validação NGFW:** Comunicação validada via NGFW com SSL Decrypt ativo
- [x] **Deploy Controlado:** Deploy realizado com sucesso em ambiente `cpe-dev`
- [x] **Certificados:** Sem necessidade de alterações manuais de certificado
- [x] **Multi-linguagem:** Implementações funcionais em Python, Java e Node.js

## 🔗 Links e Recursos

### 📂 Repositórios Git

| Linguagem   | Repositório                                                                                                                       |
| ----------- | --------------------------------------------------------------------------------------------------------------------------------- |
| **Python**  | [python-ccpe-cnsl-teste-ngfw-1](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/python-ccpe-cnsl-teste-ngfw-1) |
| **Java**    | [sboot-ccpe-cnsl-teste-ngfw](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/sboot-ccpe-cnsl-teste-ngfw)       |
| **Node.js** | [nodejs-ccpe-cnsl-quinquaginta](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/nodejs-ccpe-cnsl-quinquaginta) |

### 🏪 Componentes Tech Store

- `python-ccpe-cnsl-teste-ngfw-1`
- `sboot-ccpe-cnsl-teste-ngfw`
- `nodejs-ccpe-cnsl-quinquaginta`

## 🛠️ Tecnologias Utilizadas

### Backend

- **Python:** Flask, requests, logging
- **Java:** Spring Boot, Feign Client, Maven
- **Node.js:** Express, TypeScript, Morgan

### Infraestrutura

- **Container:** Docker
- **Orquestração:** Kubernetes (EKS)
- **Cloud:** AWS (us-east-1)
- **Deploy:** Tech Store (IDP)

### Segurança

- **Firewall:** NGFW com SSL Decrypt
- **Certificados:** SSL/TLS automático
- **Monitoramento:** Prometheus, Actuator

---

**📋 Resumo:** Este projeto demonstra com sucesso a implementação e validação da funcionalidade SSL Decrypt no NGFW através de aplicações multi-linguagem, garantindo a inspeção segura do tráfego Norte-Sul em ambiente cloud.
