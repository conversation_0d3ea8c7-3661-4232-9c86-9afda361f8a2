Título do Projeto: PIC-894 - Implementações em Python, Java e Node.js para SSL Decrypt no NGFW (Next Generation Firewall)
Autor: <PERSON>

Des<PERSON>rição Geral
Essa aplicação foi desenvolvida com o intuito de validar a funcionalidade de SSL Decrypt no NGFW (Next Generation Firewall), garantindo a inspeção segura do tráfego Norte-Sul entre workloads em cloud, conforme definido pela equipe de CPE em conjunto com as áreas de Segurança da Informação e Riscos Cibernéticos.

Aplicações entregues (Java, Node.js e Python) no ambiente cpe-dev (eks-cpe-dev) para testes dessa funcionalidade.

Funcionalidade Principal
A aplicação faz uma requisição externa para a seguinte URL pública, que retorna informações de raças de cachorro:
https://dogapi.dog/api/v2/breeds

O retorno é exibido diretamente como resposta HTTP da aplicação, permitindo verificar se o tráfego está sendo corretamente interceptado e descriptografado pelo NGFW.

Observações sobre o Certificado SSL
O certificado necessário para validação SSL já está incluído na imagem base utilizada no IDP da Porto. Portanto, nas aplicações Java e Node.js, não foi necessária nenhuma configuração adicional de truststore ou keystore para que a requisição HTTPS fosse validada com sucesso.

Já na aplicação em Python, foi necessário definir variáveis de ambiente para que o interpretador reconhecesse corretamente os certificados presentes na imagem. Essa configuração garante que bibliotecas como requests consigam validar conexões HTTPS normalmente.

Estrutura do Código das Aplicações
Python

1. Service - dog_service.py

Python

import requests
from api.core.logs import log_formatted
from api.config import REQUESTS_CA_BUNDLE

DOG_API_URL = "https://dogapi.dog/api/v2/breeds"

def get_dog_breeds():
response = requests.get(DOG_API_URL, verify=REQUESTS_CA_BUNDLE)
response.raise_for_status()
return response.json() 2. Rota - dog-breeds

Python

from flask import Blueprint, request, jsonify, Response
from api.core.logs import log_formatted
from api.core.security import requires_roles, token_required
from api.services.dog_service import get_dog_breeds

clients = Blueprint("clients", **name**)

@clients.route("/dog-breeds", methods=["GET"])
def dog_breeds():
try:
data = get_dog_breeds()
return jsonify(data), 200
except Exception as e:
log_formatted.error(f"Erro ao acessar Dog API: {e}")
return jsonify({"error": str(e)}), 500 3. Values_dev.yml

YAML

REQUESTS_CA_BUNDLE: "/etc/ssl/cert.pem"
SSL_CERT_FILE: "/etc/ssl/cert.pem"
CURL_CA_BUNDLE: "/etc/ssl/cert.pem"
Java

1. Controlador - DogApiController

Java

@RestController
@Service
public class DogApiController implements ApiApi {
@Autowired
private DogApiClient dogApiClient;

    @Override
    public ResponseEntity<ListarRacas> listarRacas() {
        try {
            var clientReturn = dogApiClient.getBreeds();
            System.out.println(clientReturn);
            return ResponseEntity.ok(clientReturn);
        } catch (FeignException feignEX) {
            return ResponseEntity
                .status(feignEX.status())
                .body(Map.of(
                    "erro", "Erro ao consumir a API",
                    "status", feignEX.status(),
                    "mensagem", feignEX.getMessage()
                ));
        } catch (Exception ex) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "erro", "Erro interno ao processar a requisição",
                    "mensagem", ex.getMessage()
                ));
        }
    }

} 2. Feign Client - DogApiClient

Java

@Component
@FeignClient(value = "sample", url = "${dog.api.url}")
public interface DogApiClient {
@RequestMapping(method = RequestMethod.GET)
DogBreedsResponseGeneratedDto getBreeds();
}
NodeJS

1. Controlador - ClienteController.ts

TypeScript

import logger from "@/config/logger";
import clienteRepository from "@/repository/clienteRepository";
import { iCliente } from "@/types/iCliente";
import { Request, Response } from "express";

class ClienteController {
obterClientes = async (\_req: Request, \_res: Response) => {
logger.debug("Obtendo clientes");
return \_res.json(await clienteRepository.obterClientes());
};

    deletarCliente = async (_req: Request, _res: Response) => {
        const { codigo } = _req.params;
        logger.debug(`Excluindo o cliente ${codigo}`);
        const retorno = await clienteRepository.deletarCliente(Number(codigo));
        return _res.status(201).json(retorno);
    };

    inserirCliente = async (_req: Request, _res: Response) => {
        const cliente: iCliente = _req.body;
        logger.debug("Inserindo o cliente %o", cliente);
        const retorno = await clienteRepository.inserirCliente(cliente);
        return _res.status(201).json(retorno);
    };

    atualizarCliente = async (_req: Request, _res: Response) => {
        const { codigo } = _req.params;
        const cliente: iCliente = _req.body;
        const retorno = await clienteRepository.atualizarCliente(
            Number(codigo),
            cliente
        );
        return _res.status(201).json(retorno);
    };

}
export default new ClienteController(); 2. Aplicação - app.ts (Completo)

TypeScript

import "express-async-errors";
import cors from "cors";
import express, { Request, Response, NextFunction } from "express";
import routes from "@/routers/routes";
import actuator from "express-actuator";
import swaggerUI from "swagger-ui-express";
import cookieParser from "cookie-parser";
import { ActuatorConfig } from "@/config/actuator";
import CONFIG from "@/config";
import { configurarSwagger } from "@/config/swagger";
import helmet from "helmet";
import { validarToken } from "@/config/SecurityConfig";
import morgan from "morgan";
import SqsConsumer from "@/samples/SqsConsumer";
import client from "prom-client";
import httpRequestDurationMicroseconds from "@/config/prometheus";

export const createApp = (): express.Application => {
const contexto = CONFIG.APP.CONTEXTO as string;
const app = express();
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get("/metrics", async (\_req, \_res) => {
\_res.setHeader("Content-Type", register.contentType);
\_res.send(await register.metrics());
});

    app.use(cors());
    app.use(express.json());
    app.use(cookieParser());
    app.use(express.urlencoded({ extended: true }));
    app.use(`/${contexto}`, actuator(ActuatorConfig));
    const swaggerDocument = configurarSwagger(`${contexto}/api/v1`);
    const options = {
        swaggerOptions: {
            url: `/${contexto}/v1/api-docs`,
        },
    };
    app.get(`/${contexto}/v1/api-docs`, (_req, res) => res.json(swaggerDocument));
    app.use(
        `/${contexto}/swagger-ui.html`,
        swaggerUI.serveFiles(swaggerDocument, options),
        swaggerUI.setup(null, options)
    );

    app.use(
        helmet({
            crossOriginEmbedderPolicy: false,
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    upgradeInsecureRequests: [],
                },
            },
        })
    );

    app.use(validarToken);
    app.use(morgan("combined"));
    app.use(`/${contexto}/api/v1`, routes);
    app.use((_req: Request, res: Response, next: NextFunction) => {
        res.locals.startEpoch = Date.now();
        const responseTimeInMs: number = Date.now() - res.locals.startEpoch;
        httpRequestDurationMicroseconds
            .labels(_req.method, _req.url, res.statusCode.toString())
            .observe(responseTimeInMs);
        next();
    });

    if (SqsConsumer) {
        SqsConsumer.startListening();
    }
    return app;

};

Deploy
A aplicação foi publicada no ambiente cpe-dev via Tech Store (IDP).

Executada em um pod dentro do EKS (us-east-1: eks-cpe-dev), com acesso à Internet para validação contra a URL externa.

Critérios de Aceite (DoD)
Aplicação responde com os dados das raças de cachorro via HTTPS.

Comunicação validada via NGFW com SSL Decrypt ativo.

Deploy realizado com sucesso em ambiente controlado (cpe-dev).

Sem necessidade de alterações manuais de certificado.

Links Úteis
Repositório Git:

https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/python-ccpe-cnsl-teste-ngfw-1

https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/sboot-ccpe-cnsl-teste-ngfw

https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/nodejs-ccpe-cnsl-quinquaginta

Componente Tech Store:

python-ccpe-cnsl-teste-ngfw-1

sboot-ccpe-cnsl-teste-ngfw

nodejs-ccpe-cnsl-quinquaginta
