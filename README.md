# PIC-894 - SSL Decrypt Implementation for NGFW

**Author:** <PERSON>

## 📋 Overview

This project implements SSL Decrypt functionality validation for NGFW (Next Generation Firewall) across multiple programming languages. The applications ensure secure inspection of North-South traffic between cloud workloads, as defined by the CPE team in collaboration with Information Security and Cyber Risk areas.

### 🎯 Objective

Validate NGFW SSL Decrypt functionality by deploying test applications (Java, Node.js, and Python) in the `cpe-dev` environment (`eks-cpe-dev`) to verify traffic interception and decryption capabilities.

### 🏗️ Architecture

For detailed architecture diagrams and system design, see: **[📊 ARCHITECTURE.md](./ARCHITECTURE.md)**

The architecture includes:

- **C4 Model diagrams** (Context and Container levels)
- **System flow diagrams** with SSL Decrypt process
- **Sequence diagrams** showing interaction flows
- **Technology stack** visualization

## 🚀 Core Functionality

The applications make external requests to the following public API:

```
https://dogapi.dog/api/v2/breeds
```

The response is displayed directly as an HTTP response, allowing verification that traffic is being correctly intercepted and decrypted by the NGFW.

## 🔐 SSL Certificate Configuration

### Java & Node.js

- **No additional configuration required**
- SSL certificates are included in the base image used in Porto's IDP
- No truststore or keystore configuration needed for HTTPS validation

### Python

- **Environment variables required** for certificate recognition
- Configuration ensures libraries like `requests` can validate HTTPS connections properly

## 🏗️ Application Structure

### Python Implementation

#### Service Layer (`dog_service.py`)

```python
import requests
from api.core.logs import log_formatted
from api.config import REQUESTS_CA_BUNDLE

DOG_API_URL = "https://dogapi.dog/api/v2/breeds"

def get_dog_breeds():
    response = requests.get(DOG_API_URL, verify=REQUESTS_CA_BUNDLE)
    response.raise_for_status()
    return response.json()
```

#### Route Handler (`dog-breeds`)

```python
from flask import Blueprint, request, jsonify, Response
from api.core.logs import log_formatted
from api.core.security import requires_roles, token_required
from api.services.dog_service import get_dog_breeds

clients = Blueprint("clients", __name__)

@clients.route("/dog-breeds", methods=["GET"])
def dog_breeds():
    try:
        data = get_dog_breeds()
        return jsonify(data), 200
    except Exception as e:
        log_formatted.error(f"Erro ao acessar Dog API: {e}")
        return jsonify({"error": str(e)}), 500
```

#### Environment Configuration (`values_dev.yml`)

```yaml
REQUESTS_CA_BUNDLE: "/etc/ssl/cert.pem"
SSL_CERT_FILE: "/etc/ssl/cert.pem"
CURL_CA_BUNDLE: "/etc/ssl/cert.pem"
```

### Java Implementation

#### Controller (`DogApiController`)

```java
@RestController
@Service
public class DogApiController implements ApiApi {
    @Autowired
    private DogApiClient dogApiClient;

    @Override
    public ResponseEntity<ListarRacas> listarRacas() {
        try {
            var clientReturn = dogApiClient.getBreeds();
            System.out.println(clientReturn);
            return ResponseEntity.ok(clientReturn);
        } catch (FeignException feignEX) {
            return ResponseEntity
                .status(feignEX.status())
                .body(Map.of(
                    "erro", "Erro ao consumir a API",
                    "status", feignEX.status(),
                    "mensagem", feignEX.getMessage()
                ));
        } catch (Exception ex) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "erro", "Erro interno ao processar a requisição",
                    "mensagem", ex.getMessage()
                ));
        }
    }
}
```

#### Feign Client (`DogApiClient`)

```java
@Component
@FeignClient(value = "sample", url = "${dog.api.url}")
public interface DogApiClient {
    @RequestMapping(method = RequestMethod.GET)
    DogBreedsResponseGeneratedDto getBreeds();
}
```

### Node.js Implementation

#### Controller (`ClienteController.ts`)

```typescript
import logger from "@/config/logger";
import clienteRepository from "@/repository/clienteRepository";
import { iCliente } from "@/types/iCliente";
import { Request, Response } from "express";

class ClienteController {
  obterClientes = async (_req: Request, _res: Response) => {
    logger.debug("Obtendo clientes");
    return _res.json(await clienteRepository.obterClientes());
  };

  // Additional methods...
}

export default new ClienteController();
```

## 🚀 Deployment

### Environment

- **Platform:** Tech Store (IDP)
- **Environment:** `cpe-dev`
- **Infrastructure:** EKS pod in `us-east-1: eks-cpe-dev`
- **Network:** Internet access for external URL validation

## ✅ Acceptance Criteria (DoD)

- [x] Application responds with dog breed data via HTTPS
- [x] Communication validated through NGFW with active SSL Decrypt
- [x] Successful deployment in controlled environment (`cpe-dev`)
- [x] No manual certificate modifications required

## 🔗 Resources

### Git Repositories

- **Python:** [python-ccpe-cnsl-teste-ngfw-1](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/python-ccpe-cnsl-teste-ngfw-1)
- **Java:** [sboot-ccpe-cnsl-teste-ngfw](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/sboot-ccpe-cnsl-teste-ngfw)
- **Node.js:** [nodejs-ccpe-cnsl-quinquaginta](https://gitportoprd.portoseguro.brasil/plataforma_devops/ccpe/cnsl/nodejs-ccpe-cnsl-quinquaginta)

### Tech Store Components

- `python-ccpe-cnsl-teste-ngfw-1`
- `sboot-ccpe-cnsl-teste-ngfw`
- `nodejs-ccpe-cnsl-quinquaginta`

## 🛠️ Technologies Used

- **Python:** Flask, requests
- **Java:** Spring Boot, Feign Client
- **Node.js:** Express, TypeScript
- **Infrastructure:** Kubernetes (EKS), Docker
- **Security:** NGFW SSL Decrypt

---

_This project validates SSL traffic inspection capabilities in enterprise firewall environments across multiple programming languages and frameworks._
